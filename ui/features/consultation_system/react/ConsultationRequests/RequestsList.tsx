import React, { useState } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'

import { Table } from '@instructure/ui-table'
import { Responsive } from '@instructure/ui-responsive'

import { Tooltip } from '@instructure/ui-tooltip'
import { TruncateText } from '@instructure/ui-truncate-text'
import { Pagination } from '@instructure/ui-pagination'
import { Flex } from '@instructure/ui-flex'
import { IconUserLine, IconEditLine, IconDownloadLine } from '@instructure/ui-icons'
import type { ConsultationRequest, ConsultationFilters } from '../types'
import { exportConsultationRequestsCSV } from '../services/consultationRequestsApi'

interface RequestsListProps {
  requests: ConsultationRequest[]
  userRole: 'student' | 'faculty'
  loading: boolean
  onRefresh: () => void
  totalCount: number
  currentPage: number
  perPage: number
  filters: ConsultationFilters
  onFiltersChange: (filters: ConsultationFilters) => void
}

const RequestsList: React.FC<RequestsListProps> = ({
  requests,
  userRole,
  loading,
  onRefresh,
  totalCount,
  currentPage,
  perPage,
  filters,
  onFiltersChange
}) => {
  const [exportLoading, setExportLoading] = useState(false)

  const handleSort = (column: string) => {
    const currentSort = filters.sort_by
    const currentDirection = filters.sort_direction || 'desc'

    let newDirection: 'asc' | 'desc' = 'desc'
    if (currentSort === column && currentDirection === 'desc') {
      newDirection = 'asc'
    }

    onFiltersChange({
      ...filters,
      sort_by: column,
      sort_direction: newDirection,
      page: 1 // Reset to first page when sorting
    })
  }

  const handlePageChange = (page: number) => {
    onFiltersChange({
      ...filters,
      page
    })
  }

  const handleExport = async () => {
    setExportLoading(true)
    try {
      await exportConsultationRequestsCSV(filters)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setExportLoading(false)
    }
  }

  const getSortDirection = (column: string) => {
    if (filters.sort_by === column) {
      return filters.sort_direction === 'asc' ? 'ascending' : 'descending'
    }
    return 'none'
  }

  const isOverdue = (request: ConsultationRequest) => {
    const now = new Date()
    const consultationDate = new Date(request.preferred_datetime)
    return request.status === 'pending' && consultationDate < now
  }

  const getCellStyle = (request: ConsultationRequest, index: number) => {
    // Priority order: overdue > pending > alternating colors
    if (isOverdue(request)) {
      return { backgroundColor: '#fef2f2' } // Light red for overdue
    }
    if (request.status === 'pending') {
      return { backgroundColor: '#fefce8' } // Light yellow for pending
    }
    // Alternating row colors - light green for even rows
    if (index % 2 === 0) {
      return { backgroundColor: '#f0f9f0' } // Light green for even rows
    }
    return { backgroundColor: '#ffffff' } // White for odd rows
  }



  const formatDate = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    } catch {
      return dateTimeString
    }
  }

  const renderActions = (request: ConsultationRequest) => {
    if (userRole === 'faculty' && request.status === 'pending') {
      return (
        <Button
          size="small"
          color="success"
          href={`/consultation_requests/faculty_dashboard?highlight=${request.id}`}
        >
          Review
        </Button>
      )
    }

    if (userRole === 'student' && request.status === 'pending') {
      return (
        <Button
          size="small"
          renderIcon={() => <IconEditLine />}
          href={`/consultation_requests/${request.id}/edit`}
        >
          Edit
        </Button>
      )
    }

    if (request.status === 'approved') {
      return (
        <Button
          size="small"
          href={`/calendar?event_id=${request.id}`}
          target="_blank"
        >
          View in Calendar
        </Button>
      )
    }

    return null
  }

  const renderDescription = (description: string) => {
    if (description.length > 50) {
      return (
        <Tooltip renderTip={description}>
          <TruncateText maxLines={2}>
            {description}
          </TruncateText>
        </Tooltip>
      )
    }
    return description
  }

  if (requests.length === 0) {
    return (
      <View as="div" textAlign="center" padding="x-large">
        <div className="empty-state">
          <div className="empty-icon">
            <IconUserLine size="large" />
          </div>
          <Heading level="h3" margin="0 0 small 0">
            No Consultation Requests
          </Heading>
          <Text>
            {userRole === 'student'
              ? "You haven't submitted any consultation requests yet. Click 'New Request' to get started."
              : "No consultation requests match your current filters. Try adjusting your search criteria."
            }
          </Text>
          {userRole === 'student' && (
            <View as="div" margin="medium 0 0 0">
              <Button
                color="primary"
                href="/consultation_requests/student_form"
              >
                Submit Your First Request
              </Button>
            </View>
          )}
        </div>
      </View>
    )
  }

  const totalPages = Math.ceil(totalCount / perPage)

  return (
    <View as="div" margin="medium 0 0 0">
      {/* Export Button */}
      <Flex justifyItems="end" margin="0 0 medium 0">
        <Button
          onClick={handleExport}
          disabled={exportLoading}
          renderIcon={() => <IconDownloadLine />}
          size="small"
        >
          {exportLoading ? 'Exporting...' : 'Export CSV'}
        </Button>
      </Flex>

      <Responsive
        query={{
          small: { maxWidth: '768px' },
          large: { minWidth: '769px' }
        }}
        props={{
          small: { layout: 'stacked' },
          large: { layout: 'fixed' }
        }}
      >
        {props => (
          <Table caption="Consultation Requests" {...props}>
            <Table.Head renderSortLabel="Sort by">
              <Table.Row>
                <Table.ColHeader
                  id="student"
                  width="15%"
                  onRequestSort={() => handleSort(userRole === 'faculty' ? 'student_name' : 'faculty_name')}
                  sortDirection={getSortDirection(userRole === 'faculty' ? 'student_name' : 'faculty_name')}
                >
                  {userRole === 'faculty' ? 'Student Name & ID' : 'Faculty Name'}
                </Table.ColHeader>
                <Table.ColHeader
                  id="date"
                  width="12%"
                  onRequestSort={() => handleSort('preferred_datetime')}
                  sortDirection={getSortDirection('preferred_datetime')}
                >
                  Date of Consultation
                </Table.ColHeader>
                <Table.ColHeader
                  id="concern"
                  width="15%"
                  onRequestSort={() => handleSort('concern_type')}
                  sortDirection={getSortDirection('concern_type')}
                >
                  Concern Type
                </Table.ColHeader>
                <Table.ColHeader id="description" width="25%">
                  Description
                </Table.ColHeader>
                <Table.ColHeader
                  id="status"
                  width="10%"
                  onRequestSort={() => handleSort('status')}
                  sortDirection={getSortDirection('status')}
                >
                  Status
                </Table.ColHeader>
                <Table.ColHeader
                  id="submitted"
                  width="12%"
                  onRequestSort={() => handleSort('created_at')}
                  sortDirection={getSortDirection('created_at')}
                >
                  Submitted
                </Table.ColHeader>
                <Table.ColHeader id="actions" width="11%">
                  Actions
                </Table.ColHeader>
              </Table.Row>
            </Table.Head>
            <Table.Body>
              {requests.map((request, index) => (
                <Table.Row key={request.id}>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <View as="div">
                      <Text weight="bold" size="small">
                        {userRole === 'student' ? request.faculty_name : request.student_name}
                      </Text>
                      {userRole === 'faculty' && (
                        <View as="div">
                          <Text size="x-small" color="secondary">
                            {request.student_id}
                          </Text>
                        </View>
                      )}
                    </View>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <Text size="small">
                      {formatDate(request.preferred_datetime)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <Text size="small">
                      {request.concern_type_display}
                    </Text>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <Text size="small">
                      {renderDescription(request.description)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <Text size="small" weight="bold">
                      {request.status_display || request.status}
                    </Text>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    <Text size="x-small" color="secondary">
                      {formatDate(request.created_at)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell style={getCellStyle(request, index)}>
                    {renderActions(request)}
                  </Table.Cell>
                </Table.Row>
              ))}
            </Table.Body>
          </Table>
        )}
      </Responsive>

      {/* Pagination */}
      {totalPages > 1 && (
        <Flex justifyItems="center" margin="medium 0 0 0">
          <Pagination
            as="nav"
            margin="small"
            variant="compact"
            labelNext="Next Page"
            labelPrev="Previous Page"
          >
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Pagination.Page
                key={page}
                onClick={() => handlePageChange(page)}
                current={page === currentPage}
              >
                {page}
              </Pagination.Page>
            ))}
          </Pagination>
        </Flex>
      )}

      {/* Results summary */}
      <Flex justifyItems="center" margin="small 0 0 0">
        <Text size="small" color="secondary">
          Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalCount)} of {totalCount} results
        </Text>
      </Flex>
    </View>
  )
}

export default RequestsList
