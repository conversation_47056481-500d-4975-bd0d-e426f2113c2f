import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { Tabs } from '@instructure/ui-tabs'
import { Badge } from '@instructure/ui-badge'
import { IconDocumentLine, IconAddLine, IconRefreshLine } from '@instructure/ui-icons'
import RequestsList from './RequestsList'
import RequestsFilters from './RequestsFilters'
import { fetchConsultationRequests } from '../services/consultationRequestsApi'
import type { ConsultationRequest, ConsultationFilters } from '../types'

interface ConsultationRequestsProps {
  currentUserId: string
  userRole: 'student' | 'faculty'
  initialRequests?: ConsultationRequest[]
  concernTypes: string[]
  statuses: string[]
}

const ConsultationRequests: React.FC<ConsultationRequestsProps> = ({
  currentUserId,
  userRole,
  initialRequests = [],
  concernTypes,
  statuses
}) => {
  const [requests, setRequests] = useState<ConsultationRequest[]>(initialRequests)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [filters, setFilters] = useState<ConsultationFilters>({})
  const [totalCount, setTotalCount] = useState(0)

  console.log('concernTypes', concernTypes)
  useEffect(() => {
    if (initialRequests.length === 0) {
      loadRequests()
    }
  }, [])

  const loadRequests = async (newFilters?: ConsultationFilters) => {
    setLoading(true)
    setError(null)
    const filtersToUse = newFilters || filters
    const result = await fetchConsultationRequests(filtersToUse)

    if (result.hasError) {
      setError(result.errorMessage || 'Failed to load consultation requests. Please try again.')
      console.error('Error loading requests:', result.errorMessage)
    } else if (result.data) {
      setRequests(result.data.requests)
      setTotalCount(result.data.total_count)
    }

    setLoading(false)
  }

  const handleFiltersChange = (newFilters: ConsultationFilters) => {
    setFilters(newFilters)
    loadRequests(newFilters)
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  const pendingCount = requests.filter(req => req.status === 'pending').length
  const approvedCount = requests.filter(req => req.status === 'approved').length

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconDocumentLine /> {userRole === 'student' ? 'My Consultation Requests' : 'Consultation Requests'}
          </Heading>
          <p>
            {userRole === 'student' 
              ? 'View and manage your consultation requests with faculty members.'
              : 'Manage consultation requests from students.'
            }
          </p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <View as="div" display="flex" margin="0 0 medium 0">
          <View as="div" display="flex" margin="0 medium 0 0">
            <Heading level="h3" margin="0">
              All Requests
              {totalCount > 0 && (
                <span style={{ marginLeft: '0.5rem', fontSize: '0.8rem', color: '#666' }}>
                  ({totalCount})
                </span>
              )}
            </Heading>
            {pendingCount > 0 && (
              <Badge
                count={pendingCount}
                type="notification"
              />
            )}
            {approvedCount > 0 && (
              <Badge
                count={approvedCount}
                type="count"
              />
            )}
          </View>
          <View as="div" display="flex">
            {userRole === 'student' && (
              <Button
                color="primary"
                href="/consultation_requests/student_form"
                margin="0 small 0 0"
              >
                New Request
              </Button>
            )}
            <Button
              onClick={() => loadRequests()}
              disabled={loading}
              margin="0 small 0 0"
            >
              Refresh
            </Button>
          </View>
        </View>

        <RequestsFilters
          filters={filters}
          concernTypes={concernTypes}
          statuses={statuses}
          userRole={userRole}
          onFiltersChange={handleFiltersChange}
          loading={loading}
        />

        {loading ? (
          <View as="div" textAlign="center" padding="large">
            <Spinner renderTitle="Loading consultation requests..." />
          </View>
        ) : (
          <RequestsList
            requests={requests}
            userRole={userRole}
            loading={loading}
            onRefresh={() => loadRequests()}
          />
        )}
      </View>
    </div>
  )
}

export default ConsultationRequests
