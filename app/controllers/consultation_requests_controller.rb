# frozen_string_literal: true

class ConsultationRequestsController < ApplicationController
  before_action :require_user
  before_action :set_consultation_request, only: [:show, :update, :destroy, :approve, :decline, :complete]
  before_action :require_student_access, only: [:create]
  before_action :require_faculty_access, only: [:approve, :decline, :complete, :faculty_dashboard]

  # GET /consultation_requests
  def index
    if @current_user.student_enrollments.active.exists?
      @requests = @current_user.student_consultation_requests.preload(:faculty, :faculty_time_slot)
    elsif @current_user.teacher_enrollments.active.exists?
      @requests = @current_user.faculty_consultation_requests.preload(:student, :faculty_time_slot)
    else
      @requests = ConsultationRequest.none
    end

    # Apply filters
    @requests = @requests.where(status: params[:status]) if params[:status].present?
    @requests = @requests.by_concern(params[:concern_type]) if params[:concern_type].present?

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      if @current_user.student_enrollments.active.exists?
        @requests = @requests.joins(:faculty).where("users.name ILIKE ?", search_term)
      else
        @requests = @requests.joins(:student).where("users.name ILIKE ? OR consultation_requests.student_number ILIKE ?", search_term, search_term)
      end
    end

    # Apply sorting
    sort_column = params[:sort_by] || 'created_at'
    sort_direction = params[:sort_direction] || 'desc'

    case sort_column
    when 'student_name'
      @requests = @requests.joins(:student).order("users.name #{sort_direction}")
    when 'faculty_name'
      @requests = @requests.joins(:faculty).order("users.name #{sort_direction}")
    when 'preferred_datetime'
      @requests = @requests.order("preferred_datetime #{sort_direction}")
    when 'status'
      @requests = @requests.order("status #{sort_direction}")
    when 'concern_type'
      @requests = @requests.order("nature_of_concern #{sort_direction}")
    else
      @requests = @requests.order("created_at #{sort_direction}")
    end

    # Get total count before pagination
    @total_count = @requests.count

    # Apply pagination
    page = params[:page]&.to_i || 1
    per_page = params[:per_page]&.to_i || 20
    per_page = [per_page, 100].min # Max 100 per page

    @requests = @requests.limit(per_page).offset((page - 1) * per_page)

    respond_to do |format|
      format.json { render json: consultation_requests_json(@requests) }
      format.html { render_consultation_requests_page }
      format.csv { export_consultation_requests_csv }
    end
  end

  # GET /consultation_requests/:id
  def show
    authorize_request_access!
    
    respond_to do |format|
      format.json { render json: consultation_request_json(@consultation_request) }
    end
  end

  # POST /consultation_requests
  def create
    @consultation_request = @current_user.student_consultation_requests.build(consultation_request_params)

    # Handle faculty_time_slot_id - it might be a composite ID like "slot_id-timestamp"
    faculty_time_slot_id = params[:consultation_request][:faculty_time_slot_id]
    if faculty_time_slot_id.present? && faculty_time_slot_id.to_s.include?('-')
      # Extract the actual faculty_time_slot_id from composite ID
      actual_slot_id = faculty_time_slot_id.to_s.split('-').first
      @consultation_request.faculty_time_slot_id = actual_slot_id
    end

    # Set the faculty from the faculty_time_slot
    if @consultation_request.faculty_time_slot
      @consultation_request.faculty = @consultation_request.faculty_time_slot.user
    end

    if @consultation_request.save
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request), status: :created }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_request.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # PATCH/PUT /consultation_requests/:id
  def update
    authorize_request_access!
    
    if @consultation_request.update(consultation_request_update_params)
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    else
      respond_to do |format|
        format.json { render json: { errors: @consultation_request.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # DELETE /consultation_requests/:id
  def destroy
    authorize_request_access!
    
    if @consultation_request.pending?
      @consultation_request.cancel!
      respond_to do |format|
        format.json { head :no_content }
      end
    else
      respond_to do |format|
        format.json { render json: { error: 'Cannot cancel non-pending request' }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/approve
  def approve
    authorize_faculty_action!
    
    begin
      @consultation_request.approve!(@current_user, params[:comment])
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/decline
  def decline
    authorize_faculty_action!
    
    comment = params[:comment]
    if comment.blank?
      respond_to do |format|
        format.json { render json: { errors: ['Comment is required when declining a request'] }, status: :unprocessable_entity }
      end
      return
    end

    begin
      @consultation_request.decline!(@current_user, comment)
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # POST /consultation_requests/:id/complete
  def complete
    authorize_faculty_action!
    
    begin
      @consultation_request.complete!(params[:completion_notes])
      respond_to do |format|
        format.json { render json: consultation_request_json(@consultation_request) }
      end
    rescue ActiveRecord::RecordInvalid => e
      respond_to do |format|
        format.json { render json: { errors: e.record.errors.full_messages }, status: :unprocessable_entity }
      end
    end
  end

  # GET /consultation_requests/faculty_dashboard
  def faculty_dashboard
    @pending_requests = @current_user.faculty_consultation_requests.pending.preload(:student, :faculty_time_slot)
    @upcoming_consultations = @current_user.upcoming_consultations.limit(10)
    @statistics = @current_user.consultation_statistics

    respond_to do |format|
      format.json {
        render json: {
          pending_requests: consultation_requests_json(@pending_requests)[:requests],
          upcoming_consultations: consultation_requests_json(@upcoming_consultations)[:requests],
          statistics: @statistics
        }
      }
      format.html { render_faculty_dashboard_page }
    end
  end

  # GET /consultation_requests/faculty/:id/time_slots
  def faculty_time_slots
    begin
      faculty = User.find(params[:id])
      date_range = parse_date_range

      formatted_slots = build_faculty_time_slots(faculty, date_range)

      # Log debug information
      Rails.logger.info "Faculty Time Slots Request - Faculty: #{faculty.id}, Date Range: #{date_range.first} to #{date_range.last}"
      Rails.logger.info "Total slots found: #{formatted_slots.count}"

      render json: {
        time_slots: formatted_slots.sort_by { |slot| slot[:datetime] },
        debug_info: build_debug_info(faculty, date_range, formatted_slots)
      }
    rescue => e
      Rails.logger.error "Error in faculty_time_slots: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render json: {
        error: e.message,
        time_slots: [],
        debug_info: { error: true, message: e.message }
      }, status: 500
    end
  end

  # GET /consultation_requests/student_form
  def student_form
    unless @current_user.student_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Student access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Student access required.'
          redirect_to root_path
        }
      end
      return
    end

    @available_faculty = @current_user.available_faculty_for_consultation.preload(:faculty_time_slots)

    respond_to do |format|
      format.json {
        render json: {
          student_info: student_info_json(@current_user),
          available_faculty: faculty_list_json(@available_faculty),
          concern_types: ConsultationRequest::NATURE_OF_CONCERNS
        }
      }
      format.html { render_student_form_page }
    end
  end

  private

  def export_consultation_requests_csv
    require 'csv'

    # Get all requests without pagination for export
    all_requests = get_base_requests_query
    all_requests = apply_filters_and_sorting(all_requests)

    csv_data = CSV.generate(headers: true) do |csv|
      # Define headers
      headers = if @current_user.student_enrollments.active.exists?
        ['Faculty Name', 'Date of Consultation', 'Concern Type', 'Description', 'Status', 'Submitted Date', 'Faculty Comment']
      else
        ['Student Name', 'Student ID', 'Date of Consultation', 'Concern Type', 'Description', 'Status', 'Submitted Date', 'Faculty Comment']
      end
      csv << headers

      # Add data rows
      all_requests.each do |request|
        row = if @current_user.student_enrollments.active.exists?
          [
            request.faculty.name,
            request.preferred_datetime.strftime('%m/%d/%Y %I:%M %p'),
            request.concern_type_display,
            request.description,
            request.status_display,
            request.created_at.strftime('%m/%d/%Y %I:%M %p'),
            request.faculty_comment || ''
          ]
        else
          [
            request.student_name,
            request.student_number,
            request.preferred_datetime.strftime('%m/%d/%Y %I:%M %p'),
            request.concern_type_display,
            request.description,
            request.status_display,
            request.created_at.strftime('%m/%d/%Y %I:%M %p'),
            request.faculty_comment || ''
          ]
        end
        csv << row
      end
    end

    filename = "consultation_requests_#{Date.current.strftime('%Y%m%d')}.csv"
    send_data csv_data,
              filename: filename,
              type: 'text/csv',
              disposition: 'attachment'
  end

  def get_base_requests_query
    if @current_user.student_enrollments.active.exists?
      @current_user.student_consultation_requests.preload(:faculty, :faculty_time_slot)
    elsif @current_user.teacher_enrollments.active.exists?
      @current_user.faculty_consultation_requests.preload(:student, :faculty_time_slot)
    else
      ConsultationRequest.none
    end
  end

  def apply_filters_and_sorting(requests)
    # Apply filters
    requests = requests.where(status: params[:status]) if params[:status].present?
    requests = requests.by_concern(params[:concern_type]) if params[:concern_type].present?

    # Apply search
    if params[:search].present?
      search_term = "%#{params[:search]}%"
      if @current_user.student_enrollments.active.exists?
        requests = requests.joins(:faculty).where("users.name ILIKE ?", search_term)
      else
        requests = requests.joins(:student).where("users.name ILIKE ? OR consultation_requests.student_number ILIKE ?", search_term, search_term)
      end
    end

    # Apply sorting
    sort_column = params[:sort_by] || 'created_at'
    sort_direction = params[:sort_direction] || 'desc'

    case sort_column
    when 'student_name'
      requests = requests.joins(:student).order("users.name #{sort_direction}")
    when 'faculty_name'
      requests = requests.joins(:faculty).order("users.name #{sort_direction}")
    when 'preferred_datetime'
      requests = requests.order("preferred_datetime #{sort_direction}")
    when 'status'
      requests = requests.order("status #{sort_direction}")
    when 'concern_type'
      requests = requests.order("nature_of_concern #{sort_direction}")
    else
      requests = requests.order("created_at #{sort_direction}")
    end

    requests
  end

  def parse_date_range
    start_date = params[:start_date].present? ? Date.parse(params[:start_date]) : Date.today
    end_date = params[:end_date].present? ? Date.parse(params[:end_date]) : (start_date + 7.days)
    start_date..end_date
  end

  def build_faculty_time_slots(faculty, date_range)
    formatted_slots = []

    # Get faculty's time slots (both recurring and specific date slots)
    recurring_slots = faculty.faculty_time_slots.available.recurring
    specific_date_slots = faculty.faculty_time_slots.available.specific_date
                                 .where(specific_date: date_range)

    # Process recurring slots
    formatted_slots.concat(process_recurring_slots(recurring_slots, date_range, faculty))

    # Process specific date slots
    formatted_slots.concat(process_specific_date_slots(specific_date_slots, faculty))

    formatted_slots
  end

  def process_recurring_slots(recurring_slots, date_range, faculty)
    slots = []
    return slots unless recurring_slots.any?

    date_range.each do |date|
      day_name = date.strftime('%A')
      recurring_slots.for_day(day_name).each do |slot|
        slot_time = build_slot_time(date, slot.start_time)
        slots << format_time_slot(slot, slot_time, faculty)
      end
    end

    slots
  end

  def process_specific_date_slots(specific_date_slots, faculty)
    specific_date_slots.map do |slot|
      slot_time = build_slot_time(slot.specific_date, slot.start_time)
      format_time_slot(slot, slot_time, faculty)
    end
  end

  def build_slot_time(date, time)
    Time.zone.local(date.year, date.month, date.day, time.hour, time.min)
  end

  def format_time_slot(slot, slot_time, faculty)
    is_booked = slot_booked?(slot, slot_time)
    has_pending = slot_has_pending?(slot, slot_time)
    is_past = slot_time < Time.current

    {
      id: "#{slot.id}-#{slot_time.to_i}",
      datetime: slot_time.iso8601,
      formatted_time: slot_time.strftime("%I:%M %p"),
      is_available: slot.is_available && !is_booked && !has_pending && !is_past,
      is_booked: is_booked,
      has_pending: has_pending,
      is_past: is_past,
      faculty_id: faculty.id,
      faculty_time_slot_id: slot.id,
      created_at: slot.created_at.iso8601,
      updated_at: slot.updated_at.iso8601
    }
  end

  def slot_booked?(slot, slot_time)
    slot.consultation_requests.where(
      status: ['approved', 'completed'],
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def slot_has_pending?(slot, slot_time)
    slot.consultation_requests.where(
      status: 'pending',
      preferred_datetime: slot_time.beginning_of_hour..slot_time.end_of_hour
    ).exists?
  end

  def build_debug_info(faculty, date_range, formatted_slots)
    recurring_count = faculty.faculty_time_slots.available.recurring.count
    specific_count = faculty.faculty_time_slots.available.specific_date
                            .where(specific_date: date_range).count

    {
      faculty_id: faculty.id,
      start_date: date_range.first,
      end_date: date_range.last,
      recurring_slots_count: recurring_count,
      specific_date_slots_count: specific_count,
      total_slots_count: formatted_slots.count
    }
  end

  def require_student_access
    unless @current_user.student_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Student access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Student access required.'
          redirect_to root_path
        }
      end
    end
  end

  def require_faculty_access
    unless @current_user.teacher_enrollments.active.exists?
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Faculty access required.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied. Faculty access required.'
          redirect_to root_path
        }
      end
    end
  end

  def set_consultation_request
    @consultation_request = ConsultationRequest.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    respond_to do |format|
      format.json { render json: { error: 'Consultation request not found' }, status: :not_found }
      format.html { 
        flash[:error] = 'Consultation request not found'
        redirect_to consultation_requests_path 
      }
    end
  end

  def authorize_request_access!
    unless @consultation_request.student == @current_user || @consultation_request.faculty == @current_user
      respond_to do |format|
        format.json { render json: { error: 'Access denied' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied'
          redirect_to consultation_requests_path
        }
      end
    end
  end

  def authorize_faculty_action!
    unless @consultation_request.faculty == @current_user
      respond_to do |format|
        format.json { render json: { error: 'Access denied. Only the assigned faculty can perform this action.' }, status: :forbidden }
        format.html {
          flash[:error] = 'Access denied'
          redirect_to consultation_requests_path
        }
      end
    end
  end

  def consultation_request_params
    params.require(:consultation_request).permit(
      :faculty_time_slot_id, :preferred_datetime, :description, :nature_of_concern, :custom_concern,
      :college_campus_institute, :department_program, :semester, :academic_year,
      :place_of_consultation, :intervention_given, :referral_made,
      :students_adviser_agreement, :prepared_by_name, :prepared_by_designation,
      :noted_by_program_chair, :noted_by_college_dean, :conformance_signature
    )
  end

  def consultation_request_update_params
    params.require(:consultation_request).permit(
      :description, :nature_of_concern, :custom_concern, :college_campus_institute,
      :department_program, :semester, :academic_year, :place_of_consultation,
      :intervention_given, :referral_made, :students_adviser_agreement,
      :prepared_by_name, :prepared_by_designation, :noted_by_program_chair,
      :noted_by_college_dean, :conformance_signature
    )
  end

  def consultation_request_json(request)
    {
      id: request.id,
      student_name: request.student_name,
      student_id: request.student_number,
      faculty_name: request.faculty.name,
      preferred_datetime: request.preferred_datetime.iso8601,
      formatted_datetime: request.formatted_preferred_datetime,
      description: request.description,
      nature_of_concern: request.nature_of_concern,
      custom_concern: request.custom_concern,
      concern_type_display: request.concern_type_display,
      status: request.status,
      status_display: request.status_display,
      faculty_comment: request.faculty_comment,
      created_at: request.created_at.iso8601,
      updated_at: request.updated_at.iso8601,
      can_be_approved: request.can_be_approved?,
      can_be_declined: request.can_be_declined?,
      can_be_completed: request.can_be_completed?,
      college_campus_institute: request.college_campus_institute,
      department_program: request.department_program,
      semester: request.semester,
      academic_year: request.academic_year,
      place_of_consultation: request.place_of_consultation,
      intervention_given: request.intervention_given,
      referral_made: request.referral_made,
      students_adviser_agreement: request.students_adviser_agreement,
      prepared_by_name: request.prepared_by_name,
      prepared_by_designation: request.prepared_by_designation,
      noted_by_program_chair: request.noted_by_program_chair,
      noted_by_college_dean: request.noted_by_college_dean,
      conformance_signature: request.conformance_signature,
      scf_number: request.scf_number
    }
  end

  def consultation_requests_json(requests)
    {
      requests: requests.map { |request| consultation_request_json(request) },
      total_count: @total_count || requests.count,
      current_page: params[:page]&.to_i || 1,
      per_page: params[:per_page]&.to_i || 20
    }
  end

  def faculty_list_json(faculty_users)
    faculty_users.map do |faculty|
      {
        id: faculty.id,
        name: faculty.name,
        department: faculty.department,
        available_slots_count: calculate_truly_available_slots_count(faculty)
      }
    end
  end

  def calculate_truly_available_slots_count(faculty)
    # Get current time for comparison
    current_time = Time.current

    # Get all available time slots for this faculty
    available_slots = faculty.faculty_time_slots.available

    truly_available_count = 0

    # Check each slot for the next 4 weeks (reasonable timeframe)
    start_date = Date.current
    end_date = start_date + 4.weeks

    available_slots.each do |slot|
      if slot.is_recurring?
        # For recurring slots, check each occurrence in the date range
        (start_date..end_date).each do |date|
          next unless date.strftime('%A') == slot.day_of_week

          # Generate time slots for this date
          slot_time = Time.zone.local(date.year, date.month, date.day, slot.start_time.hour, slot.start_time.min)
          end_time_for_slot = Time.zone.local(date.year, date.month, date.day, slot.end_time.hour, slot.end_time.min)

          # Check 30-minute intervals within the slot
          while slot_time < end_time_for_slot
            # Skip if in the past, but still increment time
            if slot_time >= current_time && !slot_booked?(slot, slot_time) && !slot_has_pending?(slot, slot_time)
              truly_available_count += 1
            end
            slot_time += 30.minutes
          end
        end
      else
        # For specific date slots
        next unless slot.specific_date && slot.specific_date >= start_date && slot.specific_date <= end_date

        slot_time = Time.zone.local(slot.specific_date.year, slot.specific_date.month, slot.specific_date.day, slot.start_time.hour, slot.start_time.min)
        end_time_for_slot = Time.zone.local(slot.specific_date.year, slot.specific_date.month, slot.specific_date.day, slot.end_time.hour, slot.end_time.min)

        # Check 30-minute intervals within the slot
        while slot_time < end_time_for_slot
          # Skip if in the past, but still increment time
          if slot_time >= current_time && !slot_booked?(slot, slot_time) && !slot_has_pending?(slot, slot_time)
            truly_available_count += 1
          end
          slot_time += 30.minutes
        end
      end
    end

    truly_available_count
  end

  def render_consultation_requests_page
    @page_title = @current_user.is_student? ? 'My Consultation Requests' : 'Consultation Requests'
    requests_data = consultation_requests_json(@requests)
    js_env({
      CONSULTATION_REQUESTS: {
        current_user_id: @current_user.id,
        user_role: @current_user.student_enrollments.active.exists? ? 'student' : 'faculty',
        requests: requests_data[:requests],
        total_count: requests_data[:total_count],
        current_page: requests_data[:current_page],
        per_page: requests_data[:per_page],
        concern_types: ConsultationRequest::NATURE_OF_CONCERNS,
        statuses: ConsultationRequest::STATUSES
      }
    })

    js_bundle :consultation_requests
    css_bundle :consultation_system
    render :index
  end

  def render_faculty_dashboard_page
    @page_title = 'Faculty Consultation Dashboard'
    js_env({
      FACULTY_DASHBOARD: {
        current_user_id: @current_user.id,
        pending_requests: consultation_requests_json(@pending_requests)[:requests],
        upcoming_consultations: consultation_requests_json(@upcoming_consultations)[:requests],
        statistics: @statistics
      }
    })

    js_bundle :faculty_consultation_dashboard
    css_bundle :consultation_system
    render :faculty_dashboard
  end

  def render_student_form_page
    @page_title = 'Request Consultation'
    js_env({
      STUDENT_CONSULTATION_FORM: {
        current_user_id: @current_user.id,
        student_info: student_info_json(@current_user),
        available_faculty: faculty_list_json(@available_faculty),
        concern_types: ConsultationRequest::NATURE_OF_CONCERNS
      }
    })

    js_bundle :student_consultation_form
    css_bundle :consultation_system
    render :student_form
  end

  def student_info_json(user)
    # Get the student's active enrollment to extract course and section information
    active_enrollment = user.student_enrollments.active.preload(:course, :course_section).first

    # Try to get student record if it exists
    student_record = user.student if user.respond_to?(:student)

    student_info = {
      name: user.name,
      student_id: user.pseudonyms.first&.unique_id || "STU#{user.id}",
      department: user.department,
      semester: student_record&.semester,
      academic_year: student_record&.standing_year,
      college_campus_institute: nil,
      section: nil
    }

    if active_enrollment
      course = active_enrollment.course
      section = active_enrollment.course_section

      # Extract course/program information
      student_info[:department_program] = course.name if course
      student_info[:section] = section.name if section
      student_info[:college_campus_institute] = course.account.name if course&.account
    end

    student_info
  end
end
